import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;

public class Main {
    public static void main(String[] args){
        Security.addProvider(new BouncyCastleProvider());
        String str = "BHPOuqZrwd4SmTOB7bu+ERG+cxGfRuA3B6EtJk+10jyWzaOhOlSkg6HAi1nsoowoyG5s/tPufq7e4AQlCswRUIazXEgk1aV2iovyBrBKtEmwH9O49Sp8LkJ+Rv1jHm4aOw6Ekx6+DkGTRRpxWYL2DtqItQ==";
        str = decrypt(pem2PrivateKey("sm2.pri.pem"), str);
        System.out.println(str);
    }

    public static String decrypt(ECPrivateKeyParameters priKey, String sm2CipherText) {
        try {
            SM2Engine engine = new SM2Engine();
            engine.init(false, priKey);
            byte[] srcByte = base642Byte(sm2CipherText);
            return new String(engine.processBlock(srcByte, 0, srcByte.length), StandardCharsets.UTF_8);
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] base642Byte(String base64Key) {
        return (new Base64()).decode(base64Key);
    }

    public static ECPrivateKeyParameters pem2PrivateKey(String filePath) {
        try {
            String pemPriStr = loadKeyFromFile(filePath);
            byte[] pkcs8Der = convertPemToPkcs8DerEcPriKey(pemPriStr);
            return convertPkcs1DerToEcPriKey(pkcs8Der);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    protected static String loadKeyFromFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (file.exists()) {
            return loadKeyFromAbsoluteFile(filePath);
        } else {
            if (filePath.startsWith("/")) {
                filePath = filePath.substring(1);
            }

            try (InputStream ins = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath)) {
                BufferedReader bReader = new BufferedReader(new InputStreamReader(ins, StandardCharsets.UTF_8));
                Throwable var5 = null;

                String var8;
                try {
                    StringBuilder sb = new StringBuilder();

                    String s;
                    while((s = bReader.readLine()) != null) {
                        sb.append(s).append("\n");
                    }

                    var8 = sb.toString();
                } catch (Throwable var33) {
                    var5 = var33;
                    throw var33;
                } finally {
                    if (bReader != null) {
                        if (var5 != null) {
                            try {
                                bReader.close();
                            } catch (Throwable var32) {
                                var5.addSuppressed(var32);
                            }
                        } else {
                            bReader.close();
                        }
                    }

                }

                return var8;
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    protected static String loadKeyFromAbsoluteFile(String filePath) {
        try (FileInputStream fin = new FileInputStream(filePath)) {
            InputStreamReader reader = new InputStreamReader(fin);
            Throwable var4 = null;

            String var9;
            try {
                BufferedReader buffReader = new BufferedReader(reader);
                Throwable var6 = null;

                try {
                    StringBuilder sb = new StringBuilder();

                    String s;
                    while((s = buffReader.readLine()) != null) {
                        sb.append(s).append("\n");
                    }

                    var9 = sb.toString();
                } catch (Throwable var56) {
                    var6 = var56;
                    throw var56;
                } finally {
                    if (buffReader != null) {
                        if (var6 != null) {
                            try {
                                buffReader.close();
                            } catch (Throwable var55) {
                                var6.addSuppressed(var55);
                            }
                        } else {
                            buffReader.close();
                        }
                    }

                }
            } catch (Throwable var58) {
                var4 = var58;
                throw var58;
            } finally {
                if (reader != null) {
                    if (var4 != null) {
                        try {
                            reader.close();
                        } catch (Throwable var54) {
                            var4.addSuppressed(var54);
                        }
                    } else {
                        reader.close();
                    }
                }

            }

            return var9;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] convertPemToPkcs8DerEcPriKey(String pemString) throws IOException {
        return convertPemToDerEcData(pemString);
    }

    private static byte[] convertPemToDerEcData(String pemString) throws IOException {
        ByteArrayInputStream bIn = new ByteArrayInputStream(pemString.getBytes(StandardCharsets.UTF_8));

        byte[] var5;
        try (PemReader pRdr = new PemReader(new InputStreamReader(bIn, StandardCharsets.UTF_8))) {
            PemObject pemObject = pRdr.readPemObject();
            var5 = pemObject.getContent();
        }

        return var5;
    }

    public static ECPrivateKeyParameters convertPkcs1DerToEcPriKey(byte[] encodedKey) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException {
        PKCS8EncodedKeySpec peks = new PKCS8EncodedKeySpec(encodedKey);
        KeyFactory kf = KeyFactory.getInstance("EC", "BC");
        BCECPrivateKey privateKey = (BCECPrivateKey)kf.generatePrivate(peks);
        ECParameterSpec ecParameterSpec = privateKey.getParameters();
        ECDomainParameters ecDomainParameters = new ECDomainParameters(ecParameterSpec.getCurve(), ecParameterSpec.getG(), ecParameterSpec.getN(), ecParameterSpec.getH());
        return new ECPrivateKeyParameters(privateKey.getD(), ecDomainParameters);
    }
}